<template>
  <div class="home-page">
    <!-- Welcome Section (简洁文字版) -->
    <div class="welcome-section-simple">
      <h1>欢迎使用学生积分管理系统</h1>
      <p>本系统用于管理学生的积分情况，通过记录学生的表现来进行加分或减分，帮助学校更好地管理学生行为。</p>
    </div>
    
    <!-- Dashboard Stats Section -->
    <el-row :gutter="20" class="dashboard-stats">
      <!-- 积分概览卡片 -->
      <el-col :xs="24" :sm="24" :md="8">
        <el-card class="stats-card" shadow="hover" v-loading="loading.stats">
          <template #header>
            <div class="card-header">
              <h3>积分概览</h3>
              <el-tag type="primary" effect="dark" size="small">本月</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic-container">
              <el-statistic title="本月处理积分记录" :value="dashboardStats.monthlyRecords || 0">
                <template #value>
                  <div class="statistic-value">
                    <span class="value-number">{{ dashboardStats.monthlyRecords || 0 }}</span>
                    <el-icon class="value-icon"><TrendCharts /></el-icon>
                  </div>
                </template>
              </el-statistic>
            </div>
            <el-divider />
            <div class="progress-container">
              <el-progress 
                :percentage="dashboardStats.processPercentage || 0" 
                :format="format" 
                :stroke-width="12" 
                :show-text="true"
                :color="progressColors"
              />
              <p class="text-muted">本月积分处理进度</p>
            </div>
            <div class="stats-grid">
              <div class="stats-item">
                <p class="stats-label">待审批</p>
                <p class="stats-value warning">{{ dashboardStats.pendingApprovals || 0 }}</p>
              </div>
              <div class="stats-item">
                <p class="stats-label">已处理</p>
                <p class="stats-value success">{{ dashboardStats.processedApprovals || 0 }}</p>
              </div>
              <div class="stats-item">
                <p class="stats-label">总积分</p>
                <p class="stats-value primary">{{ dashboardStats.totalPoints || 0 }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 班级排名卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="stats-card" shadow="hover" v-loading="loading.rankings">
          <template #header>
            <div class="card-header">
              <h3>班级排名</h3>
              <el-button type="primary" text @click="goToClassStatistics">查看全部</el-button>
            </div>
          </template>
          <div class="card-content">
            <el-empty v-if="classRankings.length === 0" description="暂无班级排名数据" />
            <el-table v-else :data="classRankings" style="width: 100%" :show-header="false">
              <el-table-column width="60">
                <template #default="scope">
                  <div class="rank-badge" :class="getRankClass(scope.row.rank)">
                    {{ scope.row.rank }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="className" />
              <el-table-column prop="points" width="80" align="right">
                <template #default="scope">
                  <span class="points-value">{{ scope.row.points }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
      
      <!-- 最近活动卡片 -->
      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="stats-card" shadow="hover" v-loading="loading.activities">
          <template #header>
            <div class="card-header">
              <h3>最近活动</h3>
              <el-button type="primary" text @click="goToPointsHistory">更多</el-button>
            </div>
          </template>
          <div class="card-content">
            <el-empty v-if="activities.length === 0" description="暂无活动记录" />
            <el-timeline v-else>
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :timestamp="activity.timestamp"
                :type="activity.type"
                :hollow="activity.hollow"
                :size="activity.size"
              >
                <div class="timeline-content">
                  {{ activity.content }}
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 班级积分柱状图区域 -->
    <div class="bar-chart-section">
      <el-card shadow="hover" class="bar-chart-card">
        <template #header>
          <div class="card-header">
            <h3>班级积分统计</h3>
            <el-button type="primary" text @click="refreshClassChart">刷新</el-button>
          </div>
        </template>
        <div ref="barChartRef" class="bar-chart" v-loading="loading.classChart"></div>
      </el-card>
    </div>

    <!-- 班级积分趋势区域 -->
    <div class="trend-chart-section">
      <el-card shadow="hover" class="trend-chart-card">
        <template #header>
          <div class="card-header">
            <h3>班级积分趋势（近12个月）</h3>
          </div>
        </template>
        <div ref="trendChartRef" class="trend-chart"></div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { 
  TrendCharts, 
  DataAnalysis, 
  User, 
  Coin, 
  Document, 
  TrendCharts as Chart, 
  Setting,
  ArrowRight
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDashboardStats, getClassRankings, getRecentActivities, getPointsApplyStats } from '@/api/system/dashboard'
import { getPointsStatistics } from '@/api/system/points'
import * as echarts from 'echarts'
import { nextTick } from 'vue'
import {findAllClassNames, getClassStatistics, getClassPointsTrend} from "@/api/system/class.js"
import { avgPoints } from '@/api/system/student.js'

const router = useRouter()

// 进度条颜色
const progressColors = [
  { color: '#f56c6c', percentage: 20 },
  { color: '#e6a23c', percentage: 40 },
  { color: '#5cb87a', percentage: 60 },
  { color: '#1989fa', percentage: 80 },
  { color: '#6f7ad3', percentage: 100 }
]

// 格式化进度条
const format = (percentage) => {
  return percentage === 100 ? '满' : `${percentage}%`
}

// 加载状态
const loading = reactive({
  stats: false,
  rankings: false,
  activities: false,
  classChart: false
})

// 数据对象
const dashboardStats = reactive({
  monthlyRecords: 0,
  processPercentage: 0,
  pendingApprovals: 0,
  processedApprovals: 0,
  totalPoints: 0
})

// 班级排名数据
const classRankings = ref([])

// 最近活动数据
const activities = ref([])

// 日期时间格式化函数
const formatDateTime = (date) => {
  if (!date) return ''
  if (typeof date === 'string') date = new Date(date)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 柱状图相关
const barChartRef = ref(null)
let barChartInstance = null

const barChartData = reactive({
  classNames: [],
  avgPoints: [],
  studentNums: []
})

// 初始化班级积分柱状图
const initBarChart = () => {
  if (barChartRef.value) {
    if (barChartInstance) {
      barChartInstance.dispose()
    }
    barChartInstance = echarts.init(barChartRef.value)
    const option = {
      title: {
        text: '',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const className = barChartData.classNames[dataIndex]
          const avgPoint = barChartData.avgPoints[dataIndex]
          const studentNum = barChartData.studentNums[dataIndex]
          return `${className}<br/>平均积分: ${avgPoint.toFixed(1)}<br/>学生人数: ${studentNum}人`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: barChartData.classNames,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        name: '平均积分'
      },
      series: [
        {
          name: '平均积分',
          type: 'bar',
          barWidth: '60%',
          data: barChartData.avgPoints,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#6e8efb' },
              { offset: 1, color: '#a777e3' }
            ])
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return params.value.toFixed(1)
            }
          }
        }
      ]
    }
    barChartInstance.setOption(option)
  }
}

// 获取最近一周日期
function getLast7Days() {
  const days = []
  const today = new Date()
  for (let i = 6; i >= 0; i--) {
    const d = new Date(today)
    d.setDate(today.getDate() - i)
    days.push(`${d.getMonth() + 1}/${d.getDate()}`)
  }
  return days
}
let classList=ref([])
const getClassAll=async () => {
  const res = await findAllClassNames()
  classList.value = res.data.data
  console.log('classList.value:', classList.value)
}

// 班级积分趋势数据
const classTrendData = reactive({
  months: [],
  series: []
})

// 初始化班级积分趋势图
const trendChartRef = ref(null)
let trendChartInstance = null

const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChartInstance) {
      trendChartInstance.dispose()
    }
    trendChartInstance = echarts.init(trendChartRef.value)
    const option = {
      title: { text: '', left: 'center' },
      tooltip: { trigger: 'axis' },
      legend: { data: classTrendData.series.map(s => s.name), top: 10 },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: classTrendData.months },
      yAxis: { type: 'value', name: '积分' },
      series: classTrendData.series.map(s => ({
        name: s.name,
        type: 'line',
        data: s.data,
        smooth: true
      }))
    }
    trendChartInstance.setOption(option)
  }
}

// 获取班级积分趋势数据
const fetchClassTrend = async () => {
  // 默认取前3名班级
  const topClassIds = classRankings.value.slice(0, 3).map(c => c.classId)
  const res = await getClassPointsTrend(topClassIds)
  if (res.data && res.data.code === 200) {
    classTrendData.months = res.data.data.months
    classTrendData.series = res.data.data.series
    nextTick(() => initTrendChart())
  }
}

onMounted(async () => {
  fetchDashboardStats()
  await fetchClassRankings()
  fetchRecentActivities()
  await getClassAll()
  await fetchClassTrend()
  await fetchClassAvgPoints()
  window.addEventListener('resize', resizeBarChart)
  window.addEventListener('resize', resizeTrendChart)
})

// 获取班级积分数据
const fetchClassAvgPoints = async () => {
  loading.classChart = true
  try {
    const res = await avgPoints()
    if (res.data && res.data.code === 200) {
      const classData = res.data.data || []
      barChartData.classNames = classData.map(item => item.className)
      barChartData.avgPoints = classData.map(item => item.avgPoints || 0)
      barChartData.studentNums = classData.map(item => item.studentNum || 0)
      nextTick(() => initBarChart())
    } else {
      console.warn('获取班级积分数据失败:', res)
      ElMessage.warning('获取班级积分数据失败')
    }
  } catch (error) {
    console.error('获取班级积分数据出错:', error)
    ElMessage.error('获取班级积分数据失败')
  } finally {
    loading.classChart = false
  }
}

// 刷新班级图表
const refreshClassChart = () => {
  fetchClassAvgPoints()
}

// 获取仪表盘统计数据
const fetchDashboardStats = async () => {
  loading.stats = true
  try {
    // 并行获取两类数据
    const [statsRes, applyStatsRes] = await Promise.all([
      getDashboardStats(),
      getPointsApplyStats()
    ])
    // 处理首页统计数据
    if (statsRes.data && statsRes.data.code === 200) {
      const data = statsRes.data.data || {}
      dashboardStats.monthlyRecords = data.monthlyRecords || 0
      // dashboardStats.processPercentage = data.processPercentage || 0 // 删除原有随机赋值
      dashboardStats.pendingApprovals = data.pendingApprovals || 0
      dashboardStats.processedApprovals = data.processedApprovals || 0
      dashboardStats.totalPoints = data.totalPoints || 0
    } else {
      console.warn('获取仪表盘数据失败:', statsRes)
      useMockDashboardStats()
    }
    // 处理积分申请统计数据，计算进度百分比
    if (applyStatsRes.data && applyStatsRes.data.code === 200) {
      const applyData = applyStatsRes.data.data || {}
      const total = applyData.totalCount || 0
      const processed = (applyData.approvedCount || 0) + (applyData.rejectedCount || 0)
      dashboardStats.processPercentage = total > 0 ? Math.round(processed / total * 100) : 0
    } else {
      dashboardStats.processPercentage = 0
    }
  } catch (error) {
    console.error('获取仪表盘数据出错:', error)
    ElMessage.warning('获取仪表盘数据失败，使用默认数据')
    useMockDashboardStats()
    dashboardStats.processPercentage = 0
  } finally {
    loading.stats = false
  }
}

// 获取班级排名数据
const fetchClassRankings = async () => {
  loading.rankings = true
  try {
    const res = await getClassRankings(5)
    if (res.data && res.data.code === 200) {
      classRankings.value = res.data.data || []
    } else {
      console.warn('获取班级排名数据失败:', res)
      // 使用模拟数据
      useMockClassRankings()
    }
  } catch (error) {
    console.error('获取班级排名数据出错:', error)
    ElMessage.warning('获取班级排名数据失败，使用默认数据')
    // 使用模拟数据
    useMockClassRankings()
  } finally {
    loading.rankings = false
  }
}

// 获取最近活动数据
const fetchRecentActivities = async () => {
  loading.activities = true
  try {
    const res = await getRecentActivities(4)
    if (res.data && res.data.code === 200) {
      activities.value = (res.data.data || []).map(item => ({
        ...item,
        timestamp: formatDateTime(item.timestamp)
      }))
    } else {
      console.warn('获取最近活动数据失败:', res)
      // 使用模拟数据
      useMockActivities()
    }
  } catch (error) {
    console.error('获取最近活动数据出错:', error)
    ElMessage.warning('获取最近活动数据失败，使用默认数据')
    // 使用模拟数据
    useMockActivities()
  } finally {
    loading.activities = false
  }
}

// 使用模拟的仪表盘数据
const useMockDashboardStats = () => {
  dashboardStats.monthlyRecords = 156
  dashboardStats.processPercentage = 68
  dashboardStats.pendingApprovals = 12
  dashboardStats.processedApprovals = 144
  dashboardStats.totalPoints = 8750
}

// 使用模拟的班级排名数据
const useMockClassRankings = () => {
  classRankings.value = [
    { rank: 1, className: '计算机科学1班', points: 560 },
    { rank: 2, className: '软件工程2班', points: 520 },
    { rank: 3, className: '信息安全1班', points: 495 },
    { rank: 4, className: '数据科学1班', points: 480 },
    { rank: 5, className: '人工智能1班', points: 460 }
  ]
}

// 使用模拟的活动数据
const useMockActivities = () => {
  activities.value = [
    {
      content: '王老师批准了3名学生的积分申请',
      timestamp: formatDateTime('2023-07-10 10:30'),
      type: 'success',
      size: 'large',
      hollow: false
    },
    {
      content: '李主任审核了5名学生的违纪记录',
      timestamp: formatDateTime('2023-07-09 14:20'),
      type: 'warning',
      size: 'normal',
      hollow: false
    },
    {
      content: '张导员提交了班级集体活动加分申请',
      timestamp: formatDateTime('2023-07-08 09:15'),
      type: 'primary',
      size: 'normal',
      hollow: false
    },
    {
      content: '系统更新了本学期积分规则',
      timestamp: formatDateTime('2023-07-01 00:00'),
      type: 'info',
      size: 'normal',
      hollow: true
    }
  ]
}

// 获取排名样式
const getRankClass = (rank) => {
  if (rank === 1) return 'rank-first'
  if (rank === 2) return 'rank-second'
  if (rank === 3) return 'rank-third'
  return ''
}

// 页面导航
const goToClassStatistics = () => {
  router.push('/dashboard/statistics/class')
}

const goToPointsHistory = () => {
  router.push('/dashboard/points/history')
}

// 导航卡片跳转
const navigateTo = (route) => {
  router.push(route)
}

const resizeBarChart = () => {
  if (barChartInstance) {
    barChartInstance.resize()
  }
}

const resizeTrendChart = () => {
  if (trendChartInstance) {
    trendChartInstance.resize()
  }
}

onUnmounted(() => {
  if (barChartInstance) {
    barChartInstance.dispose()
  }
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  window.removeEventListener('resize', resizeBarChart)
  window.removeEventListener('resize', resizeTrendChart)
})
</script>

<style scoped>
.home-page {
  padding: 0;
  background-color: #f9fbfd;
}

/* 简洁欢迎区块样式 */
.welcome-section-simple {
  padding: 40px 20px 30px 20px;
  text-align: center;
  background: none;
  color: #333;
  margin-bottom: 0;
}
.welcome-section-simple h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}
.welcome-section-simple p {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

/* Dashboard Stats Section */
.dashboard-stats {
  padding: 0 20px 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.text-muted {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.statistic-container {
  padding: 10px 0;
}

.statistic-value {
  display: flex;
  align-items: center;
}

.value-number {
  font-size: 28px;
  font-weight: bold;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.value-icon {
  margin-left: 8px;
  font-size: 20px;
  color: #67C23A;
}

.progress-container {
  padding: 15px 0;
}

.rank-badge {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  background-color: #F2F6FC;
}

.rank-first {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
}

.rank-second {
  background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
  color: white;
}

.rank-third {
  background: linear-gradient(135deg, #CD7F32, #A05A2C);
  color: white;
}

.points-value {
  font-weight: bold;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.timeline-content {
  padding: 2px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 15px;
}

.stats-item {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
}

.stats-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}

.stats-label {
  font-size: 12px;
  color: #606266;
  margin: 0 0 5px 0;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.stats-value.warning {
  color: #E6A23C;
}

.stats-value.success {
  color: #67C23A;
}

.stats-value.primary {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 为卡片内容添加动画效果 */
.card-content {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .welcome-section h1 {
    font-size: 1.8rem;
  }
  
  .welcome-section p {
    font-size: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nav-card {
    flex-direction: column;
    text-align: center;
  }
  
  .card-icon-container {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .card-arrow {
    display: none;
  }
}

/* Custom Element Plus Styles */
:deep(.el-carousel__arrow) {
  background-color: rgba(110, 142, 251, 0.7);
  border-radius: 50%;
  width: 44px;
  height: 44px;
}

:deep(.el-carousel__indicators) {
  transform: translateY(15px);
}

:deep(.el-carousel__indicator) {
  padding: 0 8px;
}

:deep(.el-carousel__indicator--active button) {
  background-color: #a777e3;
}

:deep(.el-carousel__button) {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(167, 119, 227, 0.3);
}

.bar-chart-section {
  max-width: 1200px;
  margin: 0 auto 40px auto;
  padding: 0 20px;
}
.bar-chart-card {
  border-radius: 10px;
  overflow: hidden;
}
.bar-chart {
  width: 100%;
  height: 350px;
}

.trend-chart-section {
  max-width: 1200px;
  margin: 0 auto 40px auto;
  padding: 0 20px;
}
.trend-chart-card {
  border-radius: 10px;
  overflow: hidden;
}
.trend-chart {
  width: 100%;
  height: 350px;
}
</style>
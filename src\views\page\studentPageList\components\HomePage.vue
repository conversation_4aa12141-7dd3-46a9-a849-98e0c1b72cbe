<template>
  <div class="home-page">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-banner" shadow="hover">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ studentInfo.realName }}！</h1>
          <p>学号：{{ studentInfo.studentNo }} | 班级：{{ studentInfo.className }}</p>
        </div>
        <div class="welcome-stats">
          <div class="stat-item">
            <el-icon class="stat-icon"><Trophy /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ studentInfo.points }}</div>
              <div class="stat-label">当前积分</div>
            </div>
          </div>
          <div class="stat-item">
            <el-icon class="stat-icon"><Star /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ rank.rank }}</div>
              <div class="stat-label">班级排名</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20" class="main-content">
      <!-- 左侧内容 -->
      <el-col :span="16">
        <!-- 排行榜模块 -->
        <el-card class="ranking-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>{{ showTopRanking ? '积分前十名' : '积分后十名' }}</span>
              <el-tag type="info" size="small">实时更新</el-tag>
              <el-button
                  type="primary"
                  size="small"
                  @click="toggleRanking"
                  class="toggle-ranking-btn"
              >
                {{ toggleButtonText }}
              </el-button>
            </div>
          </template>
          <div class="ranking-content">

            <div class="ranking-list">
              <div
                v-for="(student, index) in currentStudents"
                :key="student.id"
                class="ranking-item"
                :class="{
                  'current-student': student.id === studentInfo.id,
                  'bottom-ranking': !showTopRanking
                }"
              >
                <div class="rank-number">
                  <el-icon v-if="showTopRanking && index < 3" class="medal-icon" :class="`medal-${index + 1}`">
                    <Medal />
                  </el-icon>
                  <span v-else class="rank-text">
                    {{ (index + 1)  }}
                  </span>
                </div>
<!--                <el-avatar :size="40" :src="student.avatar">{{ student.name.charAt(0) }}</el-avatar>-->
                <div class="student-info">
                  <div class="student-name">{{ student.realName }}</div>
                  <div class="student-class">{{ student.className }}</div>
                </div>
                <div class="student-points">{{ student.points }}分</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 班级积分统计图表 -->
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>班级积分统计</span>
              <el-button type="primary" size="small" @click="refreshChart">刷新</el-button>
            </div>
          </template>
          <div class="chart-content">
            <div ref="chartRef" class="class-chart" v-loading="chartLoading"></div>
          </div>
        </el-card>

        <!-- 荣誉墙模块 -->
<!--        <el-card class="honor-card" shadow="hover">-->
<!--          <template #header>-->
<!--            <div class="card-header">-->
<!--              <el-icon><Medal /></el-icon>-->
<!--              <span>荣誉墙</span>-->
<!--            </div>-->
<!--          </template>-->
<!--          <div class="honor-content">-->
<!--            <div class="honor-categories">-->
<!--              <el-tag-->
<!--                v-for="category in honorCategories"-->
<!--                :key="category.type"-->
<!--                :type="category.type"-->
<!--                class="honor-tag"-->
<!--                @click="selectHonorCategory(category.value)"-->
<!--              >-->
<!--                {{ category.label }}-->
<!--              </el-tag>-->
<!--            </div>-->
<!--            <div class="honor-list">-->
<!--              <div-->
<!--                v-for="honor in filteredHonors"-->
<!--                :key="honor.id"-->
<!--                class="honor-item"-->
<!--              >-->
<!--                <el-icon class="honor-icon" :class="honor.iconClass"><Star /></el-icon>-->
<!--                <div class="honor-info">-->
<!--                  <div class="honor-title">{{ honor.title }}</div>-->
<!--                  <div class="honor-desc">{{ honor.description }}</div>-->
<!--                  <div class="honor-date">{{ honor.date }}</div>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-card>-->
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="8">
        <!-- 通知公告模块 -->
        <el-card class="notice-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Bell /></el-icon>
              <span>通知公告</span>
<!--              <el-link type="primary" :underline="false" @click="viewAllNotices">查看全部</el-link>-->
            </div>
          </template>
          <div class="notice-content">
            <div
              v-for="notice in activities"
              :key="notice.id"
              class="notice-item"
              @click="viewNoticeDetail(notice)"
            >
<!--              <div class="notice-header">-->
<!--                <el-tag :type="notice.type" size="small">{{ notice.category }}</el-tag>-->
<!--                <span class="notice-date">{{ notice.date }}</span>-->
<!--              </div>-->
              <div class="notice-title">{{ notice.activityName }}</div>
              <div class="notice-summary">{{ notice.activityText }}</div>
            </div>
          </div>
        </el-card>

        <!-- 快捷操作模块 -->
<!--        <el-card class="quick-actions-card" shadow="hover">-->
<!--          <template #header>-->
<!--            <div class="card-header">-->
<!--              <el-icon><Operation /></el-icon>-->
<!--              <span>快捷操作</span>-->
<!--            </div>-->
<!--          </template>-->
<!--          <div class="quick-actions">-->
<!--            <div-->
<!--              v-for="action in quickActions"-->
<!--              :key="action.key"-->
<!--              class="action-item"-->
<!--              @click="handleQuickAction(action.key)"-->
<!--            >-->
<!--              <el-icon class="action-icon" :class="action.iconClass">-->
<!--                <component :is="action.icon" />-->
<!--              </el-icon>-->
<!--              <div class="action-info">-->
<!--                <div class="action-title">{{ action.title }}</div>-->
<!--                <div class="action-desc">{{ action.description }}</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </el-card>-->
      </el-col>
    </el-row>
  </div>
  <el-dialog
      v-model="dialogVisible"
      title="活动详情"
      width="600"
  >
    <el-form :model="xq" label-width="auto" style="max-width: 600px">
      <el-form-item label="活动名称" >
        <el-input v-model="xq.activityName" disabled/>
      </el-form-item>
      <el-form-item label="活动内容">
        <el-input v-model="xq.activityText" disabled/>
      </el-form-item>
      <el-form-item label="活动开始时间">
        <el-input v-model="xq.startTime" disabled/>
      </el-form-item>
      <el-form-item label="活动结束时间">
        <el-input v-model="xq.stopTime" disabled/>
      </el-form-item>
      <el-form-item label="图片">
        <el-image style="width: 200px; height: 200px" :src="xq.image" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">报名</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import {ref, computed, onUnmounted, onMounted, nextTick} from 'vue'
import {
  Trophy, Star, TrendCharts, Medal, Bell, Operation,
  Document, Plus, Search, Setting
} from '@element-plus/icons-vue'
import {
  avgPoints, getClassRank,
  queryBottomTenStudentsByClass,
  queryTopTenStudents,
  queryTopTenStudentsByClass
} from "@/api/system/student.js";
import {findActivity} from "@/api/system/activity.js";
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import {signUpActivity} from "@/api/system/activityApplication.js";
const studentInfo = JSON.parse(localStorage.getItem("studentList"));
const sign=ref({
  name:studentInfo.realName,
  phone:studentInfo.phone,
  className:studentInfo.className,
  activityId:""
})
console.log( sign.value.activityId)
function onSubmit() {
  signUpActivity(sign.value).then(res=>{
    ElMessage.success(res.data.message)
  })
}
// 获取router传过来的学生信息


const rank=ref([])

getClassRank(studentInfo.classId).then(res=>{
  rank.value=res.data.data
})
console.log(studentInfo);
// 学生信息
// const studentInfo = student ? JSON.parse(student) : null;

// 排行榜类型
const rankingType = ref('class')

// 排行榜数据
const rankingList = ref([
  { id: 2, name: '李四', className: '计算机1班', points: 1380, avatar: '' },
  { id: 3, name: '王五', className: '计算机1班', points: 1320, avatar: '' },
  { id: 1, name: '张三', className: '计算机1班', points: 1250, avatar: '' },
  { id: 4, name: '赵六', className: '计算机1班', points: 1180, avatar: '' },
  { id: 5, name: '钱七', className: '计算机1班', points: 1120, avatar: '' }
])

// 荣誉类别
const honorCategories = ref([
  { label: '全部', value: 'all', type: '' },
  { label: '学习优秀', value: 'study', type: 'success' },
  { label: '活动参与', value: 'activity', type: 'warning' },
  { label: '社会实践', value: 'practice', type: 'info' }
])

const selectedHonorCategory = ref('all')

// 荣誉数据
const honorList = ref([
  {
    id: 1,
    title: '三好学生',
    description: '学习成绩优异，品德良好',
    date: '2024-12-15',
    category: 'study',
    iconClass: 'honor-gold'
  },
  {
    id: 2,
    title: '优秀班干部',
    description: '积极参与班级管理工作',
    date: '2024-11-20',
    category: 'activity',
    iconClass: 'honor-silver'
  },
  {
    id: 3,
    title: '社会实践先进个人',
    description: '暑期社会实践表现突出',
    date: '2024-09-10',
    category: 'practice',
    iconClass: 'honor-bronze'
  }
])

// 过滤后的荣誉
const filteredHonors = computed(() => {
  if (selectedHonorCategory.value === 'all') {
    return honorList.value
  }
  return honorList.value.filter(honor => honor.category === selectedHonorCategory.value)
})

// 图表相关
const chartRef = ref(null)
const chartLoading = ref(false)
let chartInstance = null

// 班级积分数据
const classData = ref({
  classNames: [],
  avgPoints: [],
  studentNums: []
})

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(chartRef.value)

    const option = {
      title: {
        text: '各班级平均积分对比',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          const dataIndex = params[0].dataIndex
          const className = classData.value.classNames[dataIndex]
          const avgPoint = classData.value.avgPoints[dataIndex]
          const studentNum = classData.value.studentNums[dataIndex]
          return `${className}<br/>平均积分: ${avgPoint.toFixed(1)}<br/>学生人数: ${studentNum}人`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: classData.value.classNames,
        axisTick: { alignWithLabel: true },
        axisLabel: {
          interval: 0,
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        name: '平均积分',
        nameTextStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '平均积分',
          type: 'bar',
          barWidth: '60%',
          data: classData.value.avgPoints,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ])
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return params.value.toFixed(1)
            },
            fontSize: 10
          }
        }
      ]
    }
    chartInstance.setOption(option)
  }
}

// 获取班级积分数据
const fetchClassData = async () => {
  chartLoading.value = true
  try {
    const res = await avgPoints()
    if (res.data && res.data.code === 200) {
      const data = res.data.data || []
      classData.value.classNames = data.map(item => item.className)
      classData.value.avgPoints = data.map(item => item.avgPoints || 0)
      classData.value.studentNums = data.map(item => item.studentNum || 0)
      nextTick(() => initChart())
    } else {
      console.warn('获取班级积分数据失败:', res)
      ElMessage.warning('获取班级积分数据失败')
    }
  } catch (error) {
    console.error('获取班级积分数据出错:', error)
    ElMessage.error('获取班级积分数据失败')
  } finally {
    chartLoading.value = false
  }
}

// 刷新图表
const refreshChart = () => {
  fetchClassData()
}

// 图表响应式处理
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}
// 通知公告数据
const noticeList = ref([
  {
    id: 1,
    title: '关于2024年度奖学金评选的通知',
    summary: '请符合条件的同学及时提交申请材料...',
    category: '重要通知',
    type: 'danger',
    date: '2024-12-20'
  },
  {
    id: 2,
    title: '期末考试安排通知',
    summary: '2024年秋季学期期末考试将于...',
    category: '考试安排',
    type: 'warning',
    date: '2024-12-18'
  },
  {
    id: 3,
    title: '寒假放假时间安排',
    summary: '根据学校安排，寒假放假时间为...',
    category: '放假通知',
    type: 'info',
    date: '2024-12-15'
  }
])

// 快捷操作
const quickActions = ref([
  {
    key: 'apply',
    title: '申请加分',
    description: '提交积分申请',
    icon: Plus,
    iconClass: 'action-primary'
  },
  {
    key: 'query',
    title: '查询记录',
    description: '查看积分记录',
    icon: Search,
    iconClass: 'action-success'
  },
  {
    key: 'application',
    title: '我的申请',
    description: '管理申请状态',
    icon: Document,
    iconClass: 'action-warning'
  },
  {
    key: 'settings',
    title: '个人设置',
    description: '修改个人信息',
    icon: Setting,
    iconClass: 'action-info'
  }
])

// 方法
const selectHonorCategory = (category) => {
  selectedHonorCategory.value = category
}

const viewAllNotices = () => {
  console.log('查看全部通知')
}

const viewNoticeDetail = (notice) => {
  sign.value.activityId=notice.id
  xq.value=notice
  dialogVisible.value = true;
  console.log('查看通知详情:', notice)
}
const dialogVisible = ref(false)
const xq=ref([])
const handleQuickAction = (actionKey) => {
  console.log('查看通知详情:', actionKey)
}
const intervalIds=ref([]);
const topStudents = ref([]);
//获取本班前十名
const findTopStudents = () => {
  queryTopTenStudentsByClass(studentInfo.classId).then((res) => {
    // 统一字段名
    if (res.data && res.data.data && res.data.data.length > 0) {
      topStudents.value = res.data.data;
    } else {
      console.log('API无数据，使用模拟数据');
    }
  })

  const id = setInterval(() => {
    queryTopTenStudentsByClass(studentInfo.classId).then((res) => {
      if (res.data && res.data.data && res.data.data.length > 0) {
        topStudents.value = res.data.data;
      }
    }).catch(error => {
      console.error('定时获取前十名失败:', error);
    });
  }, 5000);
  intervalIds.value.push(id);
};
const showTopRanking = ref(true); // true显示前十名，false显示后十名
const bottomStudents = ref([]); // 存储后十名数据

const toggleButtonText = computed(() => {
  return showTopRanking.value ? '查看后十名' : '查看前十名';
});

const toggleRanking = () => {
  showTopRanking.value = !showTopRanking.value;
  console.log('切换排名显示:', showTopRanking.value ? '前十名' : '后十名');

  // 切换到后十名时，如果还没有数据就获取
  if (!showTopRanking.value && bottomStudents.value.length === 0) {
    findBottomStudents();
  }
};

// 计算当前显示的学生列表
const currentStudents = computed(() => {
  return showTopRanking.value ? topStudents.value : bottomStudents.value;
});
//获取本班后十名
const findBottomStudents = () => {
  queryBottomTenStudentsByClass(studentInfo.classId).then((res) => {
    // 存储到后十名数据中
    bottomStudents.value = res.data.data;
    console.log('获取后十名数据:', bottomStudents.value);
  })

  const id = setInterval(() => {
    queryBottomTenStudentsByClass(studentInfo.classId).then((res) => {
      if (res.data && res.data.data && res.data.data.length > 0) {
        bottomStudents.value = res.data.data;
      }
    }).catch(error => {
      console.error('定时获取后十名失败:', error);
    });
  }, 5000);
  intervalIds.value.push(id);
};
const activities = ref([]);
findTopStudents()
function getActivities() {
  findActivity().then((res) => {
    activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
  });
  const id=setInterval(()=>{
    findActivity().then((res) => {
      activities.value = (res.data.data || []).map((item) => ({ ...item, signed: false }));
    });
  }, 5000)
  intervalIds.value.push(id);
}
onUnmounted(() => {
  intervalIds.value.forEach(id => clearInterval(id));
  intervalIds.value = []; // 清空ID数组

  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
});

onMounted(() => {
  getActivities()
  fetchClassData()
  window.addEventListener('resize', resizeChart)
});
</script>

<style scoped>
.home-page {
  padding: 0;
}

/* 欢迎横幅 */
.welcome-banner {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-banner :deep(.el-card__body) {
  padding: 30px;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.welcome-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  font-size: 32px;
  color: #ffd700;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 主要内容 */
.main-content {
  margin-top: 20px;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 排行榜卡片 */
.ranking-card {
  margin-bottom: 20px;
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 20px;
}

.chart-content {
  padding: 10px 0;
}

.class-chart {
  width: 100%;
  height: 350px;
}

.ranking-tabs {
  margin-bottom: 20px;
}

.ranking-list {
  max-height: 400px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
  cursor: pointer;
}

.ranking-item:hover {
  background: #f5f7fa;
}

.ranking-item.current-student {
  background: #e3f2fd;
  border: 1px solid #2196f3;
}
.toggle-ranking-btn {
  background: #ffffff;
  border: 1px solid #1a4f8c;
  color: #1a4f8c;
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(60, 154, 240, 0.1);
}

.toggle-ranking-btn:hover {
  background: #f0f7ff;
  border-color: #3c9af0;
  color: #3c9af0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(60, 154, 240, 0.2);
}
.rank-number {
  width: 40px;
  text-align: center;
  margin-right: 12px;
}

.medal-icon {
  font-size: 24px;
}

.medal-1 { color: #ffd700; }
.medal-2 { color: #c0c0c0; }
.medal-3 { color: #cd7f32; }

.rank-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
}

/* 切换按钮样式 */
.toggle-ranking-btn {
  margin-left: auto;
}

/* 后十名特殊样式 */
.ranking-item.bottom-ranking {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.ranking-item.bottom-ranking:hover {
  background: #fde2e2;
}

.ranking-item.bottom-ranking .rank-text {
  color: #f56c6c;
}

.ranking-item.bottom-ranking .student-points {
  color: #f56c6c;
}

.student-info {
  flex: 1;
  margin-left: 12px;
}

.student-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.student-class {
  font-size: 12px;
  color: #666;
}

.student-points {
  font-weight: bold;
  color: #2196f3;
}

/* 荣誉墙卡片 */
.honor-categories {
  margin-bottom: 16px;
}

.honor-tag {
  margin-right: 8px;
  cursor: pointer;
}

.honor-list {
  max-height: 300px;
  overflow-y: auto;
}

.honor-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fafafa;
}

.honor-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
}

.honor-gold { color: #ffd700; }
.honor-silver { color: #c0c0c0; }
.honor-bronze { color: #cd7f32; }

.honor-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.honor-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.honor-date {
  font-size: 12px;
  color: #999;
}

/* 通知公告卡片 */
.notice-card {
  margin-bottom: 20px;
}

.notice-item {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.notice-item:hover {
  background: #f0f0f0;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notice-date {
  font-size: 12px;
  color: #999;
}

.notice-title {
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.4;
}

.notice-summary {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 快捷操作卡片 */
.quick-actions {
  display: grid;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.action-item:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 24px;
  margin-right: 12px;
}

.action-primary { color: #409eff; }
.action-success { color: #67c23a; }
.action-warning { color: #e6a23c; }
.action-info { color: #909399; }

.action-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-stats {
    justify-content: center;
  }
}
</style>
